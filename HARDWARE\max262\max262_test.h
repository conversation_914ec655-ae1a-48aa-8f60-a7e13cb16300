//-----------------------------------------------------------------
// MAX262测试程序头文件
// 文件名: max262_test.h
// 作者: 系统生成
// 编写时间: 2025-01-02
// 说明: MAX262测试程序的头文件
//-----------------------------------------------------------------

#ifndef _MAX262_TEST_H
#define _MAX262_TEST_H

#include "stm32f4xx.h"

//-----------------------------------------------------------------
// 测试状态枚举
//-----------------------------------------------------------------
typedef enum {
    TEST_INIT = 0,
    TEST_BANDPASS_1K,
    TEST_HIGHPASS_1K,
    TEST_BANDPASS_2K,
    TEST_HIGHPASS_2K,
    TEST_COMPLETE
} max262_test_state_t;

//-----------------------------------------------------------------
// 函数声明
//-----------------------------------------------------------------
extern void max262_basic_test(void);
extern void max262_state_machine_test(void);
extern void max262_frequency_sweep_test(void);
extern void max262_q_sweep_test(void);
extern max262_test_state_t max262_get_test_state(void);
extern void max262_reset_test(void);

#endif

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
