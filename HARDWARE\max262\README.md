# MAX262滤波器驱动程序

## 概述

本驱动程序为STM32F4xx系列单片机配置MAX262可编程滤波器芯片。MAX262是一个双通道、8阶、连续时间有源滤波器，本驱动仅配置第一个通道的模式2（带通滤波器）和模式3（高通滤波器）。

## 硬件连接

### 引脚分配

| MAX262引脚 | STM32引脚 | 功能描述 |
|-----------|----------|----------|
| D0        | PC0      | 数据线0  |
| D1        | PC1      | 数据线1  |
| A0        | PC2      | 地址线0  |
| A1        | PC3      | 地址线1  |
| A2        | PC4      | 地址线2  |
| A3        | PC5      | 地址线3  |
| LE        | PC6      | 锁存使能 |
| WR        | PC7      | 写使能   |

### 注意事项

1. 所有引脚都配置为推挽输出模式
2. 引脚选择避免了与现有硬件模块的冲突
3. 需要确保MAX262的电源和时钟信号正确连接

## 软件使用

### 1. 初始化

```c
#include "max262.h"

// 在main函数中初始化
max262_init();
```

### 2. 配置滤波器

#### 带通滤波器（模式2）

```c
// 设置中心频率为1kHz，Q值为2.0的带通滤波器
max262_set_bandpass_filter(1.0f, 2.0f);

// 或者使用通用函数
max262_filter1_config(MAX262_MODE_2, 1.0f, 2.0f);
```

#### 高通滤波器（模式3）

```c
// 设置截止频率为2kHz，Q值为1.5的高通滤波器
max262_set_highpass_filter(2.0f, 1.5f);

// 或者使用通用函数
max262_filter1_config(MAX262_MODE_3, 2.0f, 1.5f);
```

### 3. 完整示例

```c
#include "max262.h"
#include "delay.h"

int main(void)
{
    // 系统初始化
    delay_init(168);  // 假设系统时钟168MHz
    
    // MAX262初始化
    max262_init();
    delay_ms(100);
    
    while(1)
    {
        // 配置为带通滤波器
        max262_set_bandpass_filter(1.0f, 2.0f);
        delay_ms(2000);
        
        // 配置为高通滤波器
        max262_set_highpass_filter(1.0f, 2.0f);
        delay_ms(2000);
    }
}
```

## API参考

### 初始化函数

- `void max262_init(void)` - 初始化MAX262的GPIO引脚

### 配置函数

- `void max262_filter1_config(uint8_t mode, float frequency, float q_factor)` - 通用配置函数
- `void max262_set_bandpass_filter(float frequency, float q_factor)` - 设置带通滤波器
- `void max262_set_highpass_filter(float frequency, float q_factor)` - 设置高通滤波器

### 计算函数

- `uint8_t max262_calc_fn(float frequency)` - 计算频率控制字
- `uint8_t max262_calc_qn(float q_factor)` - 计算Q值控制字

### 参数说明

- `mode`: 滤波器模式
  - `MAX262_MODE_2` (0x02): 带通滤波器
  - `MAX262_MODE_3` (0x03): 高通滤波器
- `frequency`: 频率值，单位kHz
- `q_factor`: Q值，影响滤波器的选择性

## 配置参数

### 时钟频率

当前配置的时钟频率为139.8kHz (`MAX262_FCLK1 = 0.1398f`)，如果您的硬件使用不同的时钟频率，请修改`max262.h`文件中的`MAX262_FCLK1`定义。

### 频率范围

根据MAX262的规格和当前时钟配置，支持的频率范围大约为几Hz到几十kHz。具体范围取决于时钟频率和芯片规格。

## 文件结构

```
HARDWARE/max262/
├── max262.h           # 头文件，包含引脚定义和函数声明
├── max262.c           # 主驱动文件
├── max262_example.c   # 使用示例
└── README.md          # 本说明文件
```

## 注意事项

1. 使用前必须调用`max262_init()`进行初始化
2. 频率参数单位为kHz
3. Q值建议范围：0.5 - 10.0
4. 仅支持第一个通道的模式2和模式3
5. 如需修改引脚分配，请同时修改头文件中的引脚定义

## 故障排除

1. **滤波器无响应**: 检查引脚连接和初始化是否正确
2. **滤波效果不理想**: 检查时钟频率配置和参数计算
3. **编译错误**: 确保包含了必要的头文件和系统库

## 版本历史

- V1.0 (2025-01-02): 初始版本，支持模式2和模式3
