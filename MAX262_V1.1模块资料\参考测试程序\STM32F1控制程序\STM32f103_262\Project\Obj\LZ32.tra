*** Creating Trace Output File '.\Obj\LZ32.tra' Ok.
### Preparing for ADS-LD.
### Creating ADS-LD Command Line
### List of Objects: adding '".\obj\main.o"'
### List of Objects: adding '".\obj\delay.o"'
### List of Objects: adding '".\obj\peripheralinit.o"'
### List of Objects: adding '".\obj\usart.o"'
### List of Objects: adding '".\obj\stm32f10x_it.o"'
### List of Objects: adding '".\obj\ad9226.o"'
### List of Objects: adding '".\obj\stm32f10x_gpio.o"'
### List of Objects: adding '".\obj\stm32f10x_rcc.o"'
### List of Objects: adding '".\obj\stm32f10x_usart.o"'
### List of Objects: adding '".\obj\misc.o"'
### List of Objects: adding '".\obj\core_cm3.o"'
### List of Objects: adding '".\obj\system_stm32f10x.o"'
### List of Objects: adding '".\obj\startup_stm32f10x_hd.o"'
### ADS-LD Command completed:
--cpu Cortex-M3 ".\obj\main.o" ".\obj\delay.o" ".\obj\peripheralinit.o" ".\obj\usart.o" ".\obj\stm32f10x_it.o" ".\obj\ad9226.o" ".\obj\stm32f10x_gpio.o" ".\obj\stm32f10x_rcc.o" ".\obj\stm32f10x_usart.o" ".\obj\misc.o" ".\obj\core_cm3.o" ".\obj\system_stm32f10x.o" ".\obj\startup_stm32f10x_hd.o" 

--library_type=microlib --strict --scatter ".\Obj\LZ32.sct" 

--summary_stderr --info summarysizes --map --xref --callgraph --symbols 

--info sizes --info totals --info unused --info veneers 

 --list ".\List\LZ32.map" -o .\Obj\LZ32.axf### Preparing Environment (PrepEnvAds)
### ADS-LD Output File: '.\Obj\LZ32.axf'
### ADS-LD Command File: '.\Obj\LZ32.lnp'
### Checking for dirty Components...
### Creating CmdFile '.\Obj\LZ32.lnp', Handle=0x0000090C
### Writing '.lnp' file
### ADS-LD Command file '.\Obj\LZ32.lnp' is ready.
### ADS-LD: About to start ADS-LD Thread.
### ADS-LD: executed with 0 errors
### Updating obj list
### LDADS_file() completed.
