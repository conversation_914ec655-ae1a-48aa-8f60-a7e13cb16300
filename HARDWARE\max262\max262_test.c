//-----------------------------------------------------------------
// MAX262测试程序
// 文件名: max262_test.c
// 作者: 系统生成
// 编写时间: 2025-01-02
// 说明: 用于测试MAX262驱动程序的功能
//-----------------------------------------------------------------

#include "max262.h"
#include "delay.h"

//-----------------------------------------------------------------
// 测试状态枚举
//-----------------------------------------------------------------
typedef enum {
    TEST_INIT = 0,
    TEST_BANDPASS_1K,
    TEST_HIGHPASS_1K,
    TEST_BANDPASS_2K,
    TEST_HIGHPASS_2K,
    TEST_COMPLETE
} max262_test_state_t;

//-----------------------------------------------------------------
// 全局变量
//-----------------------------------------------------------------
static max262_test_state_t test_state = TEST_INIT;
static uint32_t test_timer = 0;

//-----------------------------------------------------------------
// MAX262基本功能测试
//-----------------------------------------------------------------
void max262_basic_test(void)
{
    // 初始化MAX262
    max262_init();
    delay_ms(100);
    
    // 测试1: 带通滤波器，1kHz，Q=2.0
    max262_set_bandpass_filter(1.0f, 2.0f);
    delay_ms(1000);
    
    // 测试2: 高通滤波器，1kHz，Q=2.0
    max262_set_highpass_filter(1.0f, 2.0f);
    delay_ms(1000);
    
    // 测试3: 带通滤波器，2kHz，Q=1.5
    max262_set_bandpass_filter(2.0f, 1.5f);
    delay_ms(1000);
    
    // 测试4: 高通滤波器，2kHz，Q=1.5
    max262_set_highpass_filter(2.0f, 1.5f);
    delay_ms(1000);
}

//-----------------------------------------------------------------
// MAX262状态机测试（非阻塞）
//-----------------------------------------------------------------
void max262_state_machine_test(void)
{
    static uint32_t last_time = 0;
    uint32_t current_time = 0; // 这里应该使用系统时钟获取当前时间
    
    // 简单的时间计数（实际应用中建议使用系统时钟）
    if(current_time - last_time >= 2000) // 2秒间隔
    {
        last_time = current_time;
        
        switch(test_state)
        {
            case TEST_INIT:
                max262_init();
                test_state = TEST_BANDPASS_1K;
                break;
                
            case TEST_BANDPASS_1K:
                max262_set_bandpass_filter(1.0f, 2.0f);
                test_state = TEST_HIGHPASS_1K;
                break;
                
            case TEST_HIGHPASS_1K:
                max262_set_highpass_filter(1.0f, 2.0f);
                test_state = TEST_BANDPASS_2K;
                break;
                
            case TEST_BANDPASS_2K:
                max262_set_bandpass_filter(2.0f, 1.5f);
                test_state = TEST_HIGHPASS_2K;
                break;
                
            case TEST_HIGHPASS_2K:
                max262_set_highpass_filter(2.0f, 1.5f);
                test_state = TEST_COMPLETE;
                break;
                
            case TEST_COMPLETE:
                // 测试完成，重新开始
                test_state = TEST_BANDPASS_1K;
                break;
                
            default:
                test_state = TEST_INIT;
                break;
        }
    }
}

//-----------------------------------------------------------------
// MAX262频率扫描测试
//-----------------------------------------------------------------
void max262_frequency_sweep_test(void)
{
    float frequency;
    
    max262_init();
    delay_ms(100);
    
    // 带通滤波器频率扫描：100Hz到5kHz
    for(frequency = 0.1f; frequency <= 5.0f; frequency += 0.2f)
    {
        max262_set_bandpass_filter(frequency, 2.0f);
        delay_ms(500); // 每个频率点停留500ms
    }
    
    delay_ms(1000);
    
    // 高通滤波器频率扫描：100Hz到5kHz
    for(frequency = 0.1f; frequency <= 5.0f; frequency += 0.2f)
    {
        max262_set_highpass_filter(frequency, 2.0f);
        delay_ms(500); // 每个频率点停留500ms
    }
}

//-----------------------------------------------------------------
// MAX262 Q值扫描测试
//-----------------------------------------------------------------
void max262_q_sweep_test(void)
{
    float q_factor;
    
    max262_init();
    delay_ms(100);
    
    // 固定频率1kHz，扫描Q值
    for(q_factor = 0.5f; q_factor <= 5.0f; q_factor += 0.5f)
    {
        // 带通滤波器Q值扫描
        max262_set_bandpass_filter(1.0f, q_factor);
        delay_ms(1000);
        
        // 高通滤波器Q值扫描
        max262_set_highpass_filter(1.0f, q_factor);
        delay_ms(1000);
    }
}

//-----------------------------------------------------------------
// 获取当前测试状态（用于外部查询）
//-----------------------------------------------------------------
max262_test_state_t max262_get_test_state(void)
{
    return test_state;
}

//-----------------------------------------------------------------
// 重置测试状态
//-----------------------------------------------------------------
void max262_reset_test(void)
{
    test_state = TEST_INIT;
    test_timer = 0;
}

//-----------------------------------------------------------------
// 在main.c中的集成示例
//-----------------------------------------------------------------
/*
// 在main.c中添加以下代码：

#include "max262_test.h"

int main(void)
{
    // 系统初始化
    delay_init(168);  // 根据您的系统时钟调整
    
    // 其他硬件初始化...
    
    // MAX262测试选择（选择其中一种）
    
    // 方法1: 简单阻塞测试
    // max262_basic_test();
    
    // 方法2: 频率扫描测试
    // max262_frequency_sweep_test();
    
    // 方法3: Q值扫描测试
    // max262_q_sweep_test();
    
    // 方法4: 非阻塞状态机测试（推荐）
    max262_init(); // 初始化一次
    
    while(1)
    {
        // 您的主循环代码...
        
        // MAX262状态机测试（非阻塞）
        max262_state_machine_test();
        
        delay_ms(10); // 主循环延时
    }
}
*/

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
