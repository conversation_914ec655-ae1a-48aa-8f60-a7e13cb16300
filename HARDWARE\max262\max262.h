//-----------------------------------------------------------------
// MAX262滤波器驱动头文件
// 文件名: max262.h
// 作者: 系统生成
// 编写时间: 2025-01-02
// 修改记录: 适配STM32F4xx系列
// 说明: 仅配置第一个通道的模式2和模式3
//-----------------------------------------------------------------

#ifndef _MAX262_H
#define _MAX262_H

#include "stm32f4xx.h"
#include "sys.h"

//-----------------------------------------------------------------
// 引脚定义 - 避免与现有硬件冲突
// 使用GPIOC的未使用引脚
//-----------------------------------------------------------------

// 数据线定义 (D0, D1)
#define MAX262_D0_GPIO_PORT         GPIOC
#define MAX262_D0_GPIO_PIN          SYS_GPIO_PIN0
#define MAX262_D0_GPIO_CLK_ENABLE() do{ RCC->AHB1ENR |= 1 << 2; }while(0)  /* PC口时钟使能 */

#define MAX262_D1_GPIO_PORT         GPIOC
#define MAX262_D1_GPIO_PIN          SYS_GPIO_PIN1
#define MAX262_D1_GPIO_CLK_ENABLE() do{ RCC->AHB1ENR |= 1 << 2; }while(0)  /* PC口时钟使能 */

// 地址线定义 (A0-A3)
#define MAX262_A0_GPIO_PORT         GPIOC
#define MAX262_A0_GPIO_PIN          SYS_GPIO_PIN2
#define MAX262_A0_GPIO_CLK_ENABLE() do{ RCC->AHB1ENR |= 1 << 2; }while(0)  /* PC口时钟使能 */

#define MAX262_A1_GPIO_PORT         GPIOC
#define MAX262_A1_GPIO_PIN          SYS_GPIO_PIN3
#define MAX262_A1_GPIO_CLK_ENABLE() do{ RCC->AHB1ENR |= 1 << 2; }while(0)  /* PC口时钟使能 */

#define MAX262_A2_GPIO_PORT         GPIOC
#define MAX262_A2_GPIO_PIN          SYS_GPIO_PIN4
#define MAX262_A2_GPIO_CLK_ENABLE() do{ RCC->AHB1ENR |= 1 << 2; }while(0)  /* PC口时钟使能 */

#define MAX262_A3_GPIO_PORT         GPIOC
#define MAX262_A3_GPIO_PIN          SYS_GPIO_PIN5
#define MAX262_A3_GPIO_CLK_ENABLE() do{ RCC->AHB1ENR |= 1 << 2; }while(0)  /* PC口时钟使能 */

// 控制线定义 (LE, WR)
#define MAX262_LE_GPIO_PORT         GPIOC
#define MAX262_LE_GPIO_PIN          SYS_GPIO_PIN6
#define MAX262_LE_GPIO_CLK_ENABLE() do{ RCC->AHB1ENR |= 1 << 2; }while(0)  /* PC口时钟使能 */

#define MAX262_WR_GPIO_PORT         GPIOC
#define MAX262_WR_GPIO_PIN          SYS_GPIO_PIN7
#define MAX262_WR_GPIO_CLK_ENABLE() do{ RCC->AHB1ENR |= 1 << 2; }while(0)  /* PC口时钟使能 */

//-----------------------------------------------------------------
// IO操作宏定义
//-----------------------------------------------------------------
#define MAX262_D0(x)    sys_gpio_pin_set(MAX262_D0_GPIO_PORT, MAX262_D0_GPIO_PIN, x)
#define MAX262_D1(x)    sys_gpio_pin_set(MAX262_D1_GPIO_PORT, MAX262_D1_GPIO_PIN, x)

#define MAX262_A0(x)    sys_gpio_pin_set(MAX262_A0_GPIO_PORT, MAX262_A0_GPIO_PIN, x)
#define MAX262_A1(x)    sys_gpio_pin_set(MAX262_A1_GPIO_PORT, MAX262_A1_GPIO_PIN, x)
#define MAX262_A2(x)    sys_gpio_pin_set(MAX262_A2_GPIO_PORT, MAX262_A2_GPIO_PIN, x)
#define MAX262_A3(x)    sys_gpio_pin_set(MAX262_A3_GPIO_PORT, MAX262_A3_GPIO_PIN, x)

#define MAX262_LE(x)    sys_gpio_pin_set(MAX262_LE_GPIO_PORT, MAX262_LE_GPIO_PIN, x)
#define MAX262_WR(x)    sys_gpio_pin_set(MAX262_WR_GPIO_PORT, MAX262_WR_GPIO_PIN, x)

//-----------------------------------------------------------------
// MAX262模式定义
//-----------------------------------------------------------------
#define MAX262_MODE_2   0x02    // 模式2: 带通滤波器
#define MAX262_MODE_3   0x03    // 模式3: 高通滤波器

//-----------------------------------------------------------------
// 时钟频率定义 (根据实际硬件调整)
//-----------------------------------------------------------------
#define MAX262_FCLK1    0.1398f    // 139.8kHz时钟频率 (单位: MHz)

//-----------------------------------------------------------------
// 外部函数声明
//-----------------------------------------------------------------
extern void max262_init(void);
extern void max262_filter1_config(uint8_t mode, float frequency, float q_factor);
extern uint8_t max262_calc_fn(float frequency);
extern uint8_t max262_calc_qn(float q_factor);

// 便捷函数
extern void max262_set_bandpass_filter(float frequency, float q_factor);
extern void max262_set_highpass_filter(float frequency, float q_factor);

#endif

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
