//-----------------------------------------------------------------
// MAX262使用示例
// 文件名: max262_example.c
// 作者: 系统生成
// 编写时间: 2025-01-02
// 说明: 演示如何使用MAX262驱动程序
//-----------------------------------------------------------------

#include "max262.h"
#include "delay.h"

//-----------------------------------------------------------------
// MAX262使用示例函数
//-----------------------------------------------------------------
void max262_example(void)
{
    // 1. 初始化MAX262
    max262_init();
    
    // 等待一段时间确保初始化完成
    delay_ms(100);
    
    // 2. 配置为带通滤波器 (模式2)
    // 中心频率: 1kHz, Q值: 2.0
    max262_set_bandpass_filter(1.0f, 2.0f);
    
    delay_ms(1000);  // 等待1秒
    
    // 3. 配置为高通滤波器 (模式3)
    // 截止频率: 2kHz, Q值: 1.5
    max262_set_highpass_filter(2.0f, 1.5f);
    
    delay_ms(1000);  // 等待1秒
    
    // 4. 使用通用配置函数
    // 配置为带通滤波器，中心频率500Hz，Q值3.0
    max262_filter1_config(MAX262_MODE_2, 0.5f, 3.0f);
    
    delay_ms(1000);  // 等待1秒
    
    // 5. 配置为高通滤波器，截止频率5kHz，Q值1.0
    max262_filter1_config(MAX262_MODE_3, 5.0f, 1.0f);
}

//-----------------------------------------------------------------
// 在main函数中的使用示例
//-----------------------------------------------------------------
/*
int main(void)
{
    // 系统初始化
    delay_init(168);  // 初始化延时函数 (假设系统时钟168MHz)
    
    // MAX262示例
    max262_example();
    
    while(1)
    {
        // 主循环
        delay_ms(100);
    }
}
*/

//-----------------------------------------------------------------
// 频率扫描示例
//-----------------------------------------------------------------
void max262_frequency_sweep_example(void)
{
    float frequency;
    
    // 初始化MAX262
    max262_init();
    delay_ms(100);
    
    // 频率扫描：从100Hz到10kHz，步进100Hz
    for(frequency = 0.1f; frequency <= 10.0f; frequency += 0.1f)
    {
        // 设置为带通滤波器，Q值固定为2.0
        max262_set_bandpass_filter(frequency, 2.0f);
        
        // 每个频率点停留100ms
        delay_ms(100);
    }
}

//-----------------------------------------------------------------
// Q值扫描示例
//-----------------------------------------------------------------
void max262_q_sweep_example(void)
{
    float q_factor;
    
    // 初始化MAX262
    max262_init();
    delay_ms(100);
    
    // Q值扫描：从0.5到10.0，步进0.5
    for(q_factor = 0.5f; q_factor <= 10.0f; q_factor += 0.5f)
    {
        // 设置为带通滤波器，中心频率固定为1kHz
        max262_set_bandpass_filter(1.0f, q_factor);
        
        // 每个Q值停留200ms
        delay_ms(200);
    }
}

//-----------------------------------------------------------------
// 模式切换示例
//-----------------------------------------------------------------
void max262_mode_switch_example(void)
{
    // 初始化MAX262
    max262_init();
    delay_ms(100);
    
    while(1)
    {
        // 带通滤波器模式，1kHz，Q=2.0
        max262_set_bandpass_filter(1.0f, 2.0f);
        delay_ms(2000);  // 保持2秒
        
        // 高通滤波器模式，1kHz，Q=2.0
        max262_set_highpass_filter(1.0f, 2.0f);
        delay_ms(2000);  // 保持2秒
    }
}

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
