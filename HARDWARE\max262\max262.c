//-----------------------------------------------------------------
// MAX262滤波器驱动程序
// 文件名: max262.c
// 作者: 系统生成
// 编写时间: 2025-01-02
// 修改记录: 适配STM32F4xx系列，仅配置第一个通道
// 说明: 支持模式2(带通)和模式3(高通)滤波器
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// 头文件包含
//-----------------------------------------------------------------
#include "max262.h"
#include "delay.h"
#include <math.h>

//-----------------------------------------------------------------
// 私有函数声明
//-----------------------------------------------------------------
static void max262_gpio_init(void);
static void max262_write_data(uint8_t data);
static void max262_write_address(uint8_t address);
static void max262_delay_ns(uint32_t ns);

//-----------------------------------------------------------------
// MAX262初始化函数
//-----------------------------------------------------------------
// 功能描述: 初始化MAX262的GPIO引脚
// 输入参数: 无
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 必须在使用MAX262前调用此函数
//-----------------------------------------------------------------
void max262_init(void)
{
    max262_gpio_init();
    
    // 初始化控制信号状态
    MAX262_LE(1);    // 锁存使能拉高
    MAX262_WR(1);    // 写信号拉高
    
    max262_delay_ns(1000);  // 等待稳定
}

//-----------------------------------------------------------------
// GPIO初始化函数
//-----------------------------------------------------------------
static void max262_gpio_init(void)
{
    // 使能GPIO时钟
    MAX262_D0_GPIO_CLK_ENABLE();
    MAX262_D1_GPIO_CLK_ENABLE();
    MAX262_A0_GPIO_CLK_ENABLE();
    MAX262_A1_GPIO_CLK_ENABLE();
    MAX262_A2_GPIO_CLK_ENABLE();
    MAX262_A3_GPIO_CLK_ENABLE();
    MAX262_LE_GPIO_CLK_ENABLE();
    MAX262_WR_GPIO_CLK_ENABLE();
    
    // 配置数据线为输出模式
    sys_gpio_set(MAX262_D0_GPIO_PORT, MAX262_D0_GPIO_PIN, SYS_GPIO_MODE_OUT, 
                 SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_NONE);
    sys_gpio_set(MAX262_D1_GPIO_PORT, MAX262_D1_GPIO_PIN, SYS_GPIO_MODE_OUT, 
                 SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_NONE);
    
    // 配置地址线为输出模式
    sys_gpio_set(MAX262_A0_GPIO_PORT, MAX262_A0_GPIO_PIN, SYS_GPIO_MODE_OUT, 
                 SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_NONE);
    sys_gpio_set(MAX262_A1_GPIO_PORT, MAX262_A1_GPIO_PIN, SYS_GPIO_MODE_OUT, 
                 SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_NONE);
    sys_gpio_set(MAX262_A2_GPIO_PORT, MAX262_A2_GPIO_PIN, SYS_GPIO_MODE_OUT, 
                 SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_NONE);
    sys_gpio_set(MAX262_A3_GPIO_PORT, MAX262_A3_GPIO_PIN, SYS_GPIO_MODE_OUT, 
                 SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_NONE);
    
    // 配置控制线为输出模式
    sys_gpio_set(MAX262_LE_GPIO_PORT, MAX262_LE_GPIO_PIN, SYS_GPIO_MODE_OUT, 
                 SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_NONE);
    sys_gpio_set(MAX262_WR_GPIO_PORT, MAX262_WR_GPIO_PIN, SYS_GPIO_MODE_OUT, 
                 SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_NONE);
}

//-----------------------------------------------------------------
// 频率控制字计算函数
//-----------------------------------------------------------------
// 功能描述: 根据频率计算控制字N
// 输入参数: frequency - 截止频率(单位: kHz)
// 返 回 值: 频率控制字N
// 全局变量: 无
// 注意事项: 根据芯片手册的公式计算
//-----------------------------------------------------------------
uint8_t max262_calc_fn(float frequency)
{
    uint8_t fn;
    // 根据MAX262手册公式: fn = (fclk * 637) - 26
    // 其中 fclk 单位为MHz，frequency 单位为kHz
    fn = (uint8_t)((MAX262_FCLK1 * 637.0f) - 26.0f);
    return fn;
}

//-----------------------------------------------------------------
// Q值控制字计算函数
//-----------------------------------------------------------------
// 功能描述: 根据Q值计算控制字N
// 输入参数: q_factor - Q值
// 返 回 值: Q值控制字N
// 全局变量: 无
// 注意事项: 根据芯片手册的公式计算
//-----------------------------------------------------------------
uint8_t max262_calc_qn(float q_factor)
{
    uint8_t qn;
    // 根据MAX262手册公式: qn = 128 - (64/Q)
    qn = (uint8_t)(128.0f - (64.0f / q_factor));
    return qn;
}

//-----------------------------------------------------------------
// 写数据函数
//-----------------------------------------------------------------
static void max262_write_data(uint8_t data)
{
    // 设置数据线
    MAX262_D0((data & 0x01) ? 1 : 0);
    MAX262_D1((data & 0x02) ? 1 : 0);
}

//-----------------------------------------------------------------
// 写地址函数
//-----------------------------------------------------------------
static void max262_write_address(uint8_t address)
{
    // 设置地址线
    MAX262_A0((address & 0x01) ? 1 : 0);
    MAX262_A1((address & 0x02) ? 1 : 0);
    MAX262_A2((address & 0x04) ? 1 : 0);
    MAX262_A3((address & 0x08) ? 1 : 0);
}

//-----------------------------------------------------------------
// 纳秒级延时函数
//-----------------------------------------------------------------
static void max262_delay_ns(uint32_t ns)
{
    // 简单的延时实现，实际项目中可能需要更精确的延时
    volatile uint32_t i;
    for(i = 0; i < (ns / 10); i++)
    {
        __NOP();  // 空操作
    }
}

//-----------------------------------------------------------------
// 滤波器1配置函数
//-----------------------------------------------------------------
// 功能描述: 配置滤波器1的模式、频率和Q值
// 输入参数: mode - 滤波器模式 (MAX262_MODE_2 或 MAX262_MODE_3)
//          frequency - 截止频率 (单位: kHz)
//          q_factor - Q值
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 仅支持模式2和模式3
//-----------------------------------------------------------------
void max262_filter1_config(uint8_t mode, float frequency, float q_factor)
{
    uint8_t i;
    uint8_t mask = 0x03;
    uint8_t fn, qn;

    // 检查模式是否有效
    if(mode != MAX262_MODE_2 && mode != MAX262_MODE_3)
    {
        return;  // 无效模式，直接返回
    }

    // 计算频率和Q值控制字
    fn = max262_calc_fn(frequency);
    qn = max262_calc_qn(q_factor);

    // 使能锁存器
    MAX262_LE(1);
    max262_delay_ns(200);
    MAX262_WR(1);
    max262_delay_ns(200);

    // 步骤1: 写入模式配置 (地址0)
    max262_write_address(0);        // 设置地址为0
    max262_delay_ns(200);
    MAX262_WR(0);                   // 写使能
    max262_delay_ns(200);
    max262_write_data(mode & 0x03); // 写入模式数据
    max262_delay_ns(200);
    MAX262_WR(1);                   // 写禁止
    max262_delay_ns(200);

    // 步骤2: 写入频率控制字 (地址1-3)
    for(i = 0; i < 3; i++)
    {
        max262_write_address(i + 1);    // 设置地址1-3
        max262_delay_ns(200);
        MAX262_WR(0);                   // 写使能
        max262_delay_ns(200);
        // 将频率控制字分成3个2位数据写入
        max262_write_data((fn & mask) >> (2 * i));
        max262_delay_ns(200);
        MAX262_WR(1);                   // 写禁止
        max262_delay_ns(200);
        mask = mask << 2;               // 移位准备下一个2位数据
    }

    // 步骤3: 写入Q值控制字 (地址4-7)
    mask = 0x03;  // 重置掩码
    for(i = 0; i < 4; i++)
    {
        max262_write_address(i + 4);    // 设置地址4-7
        max262_delay_ns(200);
        MAX262_WR(0);                   // 写使能
        max262_delay_ns(200);
        // 将Q值控制字分成4个2位数据写入
        max262_write_data((qn & mask) >> (2 * i));
        max262_delay_ns(200);
        MAX262_WR(1);                   // 写禁止
        max262_delay_ns(200);
        mask = mask << 2;               // 移位准备下一个2位数据
    }
}

//-----------------------------------------------------------------
// 设置带通滤波器函数
//-----------------------------------------------------------------
// 功能描述: 快速设置为带通滤波器模式
// 输入参数: frequency - 中心频率 (单位: kHz)
//          q_factor - Q值
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 这是一个便捷函数
//-----------------------------------------------------------------
void max262_set_bandpass_filter(float frequency, float q_factor)
{
    max262_filter1_config(MAX262_MODE_2, frequency, q_factor);
}

//-----------------------------------------------------------------
// 设置高通滤波器函数
//-----------------------------------------------------------------
// 功能描述: 快速设置为高通滤波器模式
// 输入参数: frequency - 截止频率 (单位: kHz)
//          q_factor - Q值
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 这是一个便捷函数
//-----------------------------------------------------------------
void max262_set_highpass_filter(float frequency, float q_factor)
{
    max262_filter1_config(MAX262_MODE_3, frequency, q_factor);
}

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
